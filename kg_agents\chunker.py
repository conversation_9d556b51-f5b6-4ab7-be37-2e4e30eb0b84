from typing import List, Dict, Any
import tiktoken
from nltk.tokenize import sent_tokenize # Ensure nltk is installed and 'punkt' downloaded
import nltk
import os

current_file_dir = os.path.dirname(__file__)

CONFIG_PATH = os.path.join(current_file_dir, '', 'config.py')

from kg_agents.config import config_manager # For TOKENIZER_NAME

# Download punkt tokenizer if not already present
try:
    nltk.data.find('tokenizers/punkt')
except LookupError:
    nltk.download('punkt')


class Chunker:
    def __init__(self):
        self.tokenizer_name = config_manager.get_tokenizer_name()
        try:
            self.tokenizer = tiktoken.get_encoding(self.tokenizer_name)
        except Exception as e:
            print(f"Warning: Could not load tokenizer {self.tokenizer_name}. Using cl100k_base. Error: {e}")
            self.tokenizer = tiktoken.get_encoding("cl100k_base")


    def split_into_chunks(self, text: str, *, max_chunks: int = 45, min_tokens_per_chunk: int = 500) -> List[Dict[str, Any]]:
        """Greedy sentence-aware chunking."""
        if not text:
            return []
            
        sentences = sent_tokenize(text)
        if not sentences:
            return [{"id": 0, "text": text, "token_count": len(self.tokenizer.encode(text))}]


        chunks, buf, buf_tokens = [], [], 0

        for sent_idx, sent in enumerate(sentences):
            sent_tokens = len(self.tokenizer.encode(sent))
            
            # If buffer already large enough AND adding this sentence would exceed 2*min_tokens_per_chunk, flush
            if buf_tokens >= min_tokens_per_chunk and buf_tokens + sent_tokens > 2 * min_tokens_per_chunk:
                if buf: # Ensure buffer is not empty before appending
                    chunk_text = " ".join(buf)
                    chunks.append({
                        "id": f"chunk_{len(chunks)}", 
                        "text": chunk_text,
                        "token_count": buf_tokens
                    })
                buf, buf_tokens = [sent], sent_tokens
            else:
                buf.append(sent)
                buf_tokens += sent_tokens

        if buf: # Append any remaining buffer content
            chunk_text = " ".join(buf)
            chunks.append({
                "id": f"chunk_{len(chunks)}",
                "text": chunk_text,
                "token_count": buf_tokens
            })

        # Consolidate to max_chunks if necessary (simple consolidation)
        if len(chunks) > max_chunks:
            # This is a simplistic way to consolidate. More sophisticated methods might be needed.
            # For now, we'll take the first `max_chunks` if over limit,
            # or implement a more robust consolidation.
            # The original consolidation logic might be too aggressive or lose context.
            # Let's re-evaluate the consolidation logic for better semantic coherence.
            # For now, if it exceeds, we might just log a warning or implement a simpler split.

            # Original consolidation:
            # sentences_per_chunk = len(sentences) // max_chunks + (len(sentences) % max_chunks > 0)
            # new_chunks = []
            # for i in range(max_chunks):
            #     start_idx = i * sentences_per_chunk
            #     end_idx = (i + 1) * sentences_per_chunk
            #     chunk_sents = sentences[start_idx:end_idx]
            #     if chunk_sents:
            #         chunk_text = " ".join(chunk_sents)
            #         new_chunks.append({
            #             "id": f"consolidated_chunk_{i}",
            #             "text": chunk_text,
            #             "token_count": len(self.tokenizer.encode(chunk_text))
            #         })
            # chunks = new_chunks
            print(f"Warning: Number of generated chunks ({len(chunks)}) exceeded max_chunks ({max_chunks}). Further consolidation logic might be needed.")
            # Truncate for now if it's critical, or improve consolidation
            chunks = chunks[:max_chunks]


        # print(f" Split into {len(chunks)} chunks (>= {min_tokens_per_chunk} tokens each where possible)")
        return chunks

# Example:
# chunker_instance = Chunker()
# my_text = "This is sentence one. This is sentence two. ..."
# text_chunks = chunker_instance.split_into_chunks(my_text)
