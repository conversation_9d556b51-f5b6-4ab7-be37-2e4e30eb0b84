"""Markdown converter module for converting JSON chunks to markdown files."""

import json
import os
from pathlib import Path
from typing import List, Dict, Any, Optional


class MarkdownConverter:
    """Handles conversion of JSON chunks to markdown files."""
    
    def __init__(self, staging_folder: str = "staging", raw_md_folder: str = "raw_md"):
        """
        Initialize the markdown converter.
        
        Args:
            staging_folder: Path to the staging folder containing chunk folders
            raw_md_folder: Path to the folder where markdown files will be stored
        """
        self.staging_folder = Path(staging_folder)
        self.raw_md_folder = Path(raw_md_folder)
        
        # Ensure raw_md folder exists
        self.raw_md_folder.mkdir(exist_ok=True)
        
        # Ensure staging folder exists
        if not self.staging_folder.exists():
            raise FileNotFoundError(f"Staging folder not found: {self.staging_folder}")
    
    def get_chunk_folders(self) -> List[Path]:
        """
        Get all chunk folders from the staging directory and assign them indices.
        
        Returns:
            List of chunk folder paths sorted by their index
        """
        chunk_folders = []
        
        for folder in self.staging_folder.iterdir():
            if folder.is_dir() and folder.name.startswith("chunks_"):
                chunk_folders.append(folder)
        
        # Sort folders by their index (extracted from folder name)
        def extract_index(folder_path: Path) -> int:
            try:
                # Extract number from "chunks_n_name_files" format
                parts = folder_path.name.split("_")
                if len(parts) >= 2 and parts[0] == "chunks":
                    return int(parts[1])
            except (ValueError, IndexError):
                pass
            return 0
        
        chunk_folders.sort(key=extract_index)
        return chunk_folders
    
    def extract_text_from_json_files(self, folder_path: Path) -> List[str]:
        """
        Extract text content from all JSON files in a folder.
        
        Args:
            folder_path: Path to the folder containing JSON chunk files
            
        Returns:
            List of text content from each chunk
        """
        texts = []
        
        # Get all JSON files and sort them by chunk index
        json_files = [f for f in folder_path.iterdir() if f.suffix.lower() == '.json']
        
        def extract_chunk_index(file_path: Path) -> int:
            try:
                # Extract number from "chunk_n.json" format
                stem = file_path.stem  # removes .json extension
                if stem.startswith("chunk_"):
                    return int(stem.split("_")[1])
            except (ValueError, IndexError):
                pass
            return 0
        
        json_files.sort(key=extract_chunk_index)
        
        for json_file in json_files:
            try:
                with open(json_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    
                # Extract the "text" key value
                if "text" in data:
                    texts.append(data["text"])
                else:
                    print(f"Warning: No 'text' key found in {json_file}")
                    
            except Exception as e:
                print(f"Error reading {json_file}: {e}")
                continue
        
        return texts
    
    def create_markdown_file(self, texts: List[str], original_folder_name: str, index: int) -> Path:
        """
        Create a markdown file from extracted texts.
        
        Args:
            texts: List of text content to include in the markdown
            original_folder_name: Name of the original chunk folder
            index: Index of the folder
            
        Returns:
            Path to the created markdown file
        """
        # Create filename: md_original_folder_name_n.md
        filename = f"md_{original_folder_name}_{index}.md"
        md_file_path = self.raw_md_folder / filename
        
        # Create markdown content
        markdown_content = []
        
        # Add title
        markdown_content.append(f"# {original_folder_name.replace('_', ' ').title()}")
        markdown_content.append("")
        markdown_content.append(f"*Generated from folder: {original_folder_name}*")
        markdown_content.append(f"*Folder index: {index}*")
        markdown_content.append("")
        markdown_content.append("---")
        markdown_content.append("")
        
        # Add each chunk as a section
        for i, text in enumerate(texts):
            markdown_content.append(f"## Chunk {i}")
            markdown_content.append("")
            markdown_content.append(text)
            markdown_content.append("")
            markdown_content.append("---")
            markdown_content.append("")
        
        # Write to file
        with open(md_file_path, 'w', encoding='utf-8') as f:
            f.write('\n'.join(markdown_content))
        
        return md_file_path
    
    def process_all_folders(self) -> Dict[str, Any]:
        """
        Process all chunk folders and convert them to markdown files.
        
        Returns:
            Dictionary with processing results
        """
        chunk_folders = self.get_chunk_folders()
        
        if not chunk_folders:
            print("No chunk folders found in staging directory.")
            return {
                "status": "no_folders",
                "processed_folders": 0,
                "created_files": []
            }
        
        print(f"Found {len(chunk_folders)} chunk folders to process.")
        
        created_files = []
        processed_count = 0
        
        # Process each folder with its index
        for index, folder_path in enumerate(chunk_folders):
            try:
                print(f"Processing folder {index}: {folder_path.name}")
                
                # Extract texts from JSON files
                texts = self.extract_text_from_json_files(folder_path)
                
                if not texts:
                    print(f"Warning: No text content found in {folder_path.name}")
                    continue
                
                # Create markdown file
                md_file_path = self.create_markdown_file(texts, folder_path.name, index)
                created_files.append(str(md_file_path))
                processed_count += 1
                
                print(f"Created markdown file: {md_file_path.name} ({len(texts)} chunks)")
                
            except Exception as e:
                print(f"Error processing folder {folder_path.name}: {e}")
                continue
        
        result = {
            "status": "success",
            "processed_folders": processed_count,
            "total_folders": len(chunk_folders),
            "created_files": created_files
        }
        
        print(f"\nProcessing complete:")
        print(f"  Processed folders: {processed_count}/{len(chunk_folders)}")
        print(f"  Created markdown files: {len(created_files)}")
        
        return result
    
    def process_specific_folder(self, folder_index: int) -> Optional[Path]:
        """
        Process a specific folder by its index.
        
        Args:
            folder_index: Index of the folder to process
            
        Returns:
            Path to the created markdown file, or None if failed
        """
        chunk_folders = self.get_chunk_folders()
        
        if folder_index >= len(chunk_folders):
            print(f"Error: Folder index {folder_index} is out of range. Available indices: 0-{len(chunk_folders)-1}")
            return None
        
        folder_path = chunk_folders[folder_index]
        
        try:
            print(f"Processing folder {folder_index}: {folder_path.name}")
            
            # Extract texts from JSON files
            texts = self.extract_text_from_json_files(folder_path)
            
            if not texts:
                print(f"Warning: No text content found in {folder_path.name}")
                return None
            
            # Create markdown file
            md_file_path = self.create_markdown_file(texts, folder_path.name, folder_index)
            
            print(f"Created markdown file: {md_file_path.name} ({len(texts)} chunks)")
            return md_file_path
            
        except Exception as e:
            print(f"Error processing folder {folder_path.name}: {e}")
            return None
