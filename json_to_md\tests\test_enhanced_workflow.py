#!/usr/bin/env python3
"""
Test script for the enhanced workflow.
This script tests the markdown conversion functionality.
"""

import os
import sys
from pathlib import Path
from kg_agents.md_converter import MarkdownConverter


def test_enhanced_workflow():
    """Test the enhanced workflow functionality."""
    
    print("Testing Enhanced Workflow")
    print("=" * 40)
    
    try:
        # Initialize converter
        converter = MarkdownConverter()
        
        # Test 1: Check if staging folder exists
        print("Test 1: Checking staging folder...")
        if not converter.staging_folder.exists():
            print("❌ Staging folder not found. Creating sample data...")
            # Import and run sample data creation
            try:
                from json_to_md.tests.create_sample_data import create_sample_chunks
                create_sample_chunks()
                print("✅ Sample data created successfully")
            except Exception as e:
                print(f"❌ Failed to create sample data: {e}")
                return False
        else:
            print("✅ Staging folder found")
        
        # Test 2: Get chunk folders
        print("\nTest 2: Getting chunk folders...")
        chunk_folders = converter.get_chunk_folders()
        if chunk_folders:
            print(f"✅ Found {len(chunk_folders)} chunk folders:")
            for i, folder in enumerate(chunk_folders):
                print(f"   {i}: {folder.name}")
        else:
            print("❌ No chunk folders found")
            return False
        
        # Test 3: Test text extraction from first folder
        print("\nTest 3: Testing text extraction...")
        if chunk_folders:
            first_folder = chunk_folders[0]
            texts = converter.extract_text_from_json_files(first_folder)
            if texts:
                print(f"✅ Extracted {len(texts)} text chunks from {first_folder.name}")
                print(f"   First chunk preview: {texts[0][:100]}...")
            else:
                print(f"❌ No text extracted from {first_folder.name}")
                return False
        
        # Test 4: Test markdown file creation
        print("\nTest 4: Testing markdown file creation...")
        if chunk_folders and texts:
            md_file = converter.create_markdown_file(texts, first_folder.name, 0)
            if md_file.exists():
                print(f"✅ Created markdown file: {md_file.name}")
                
                # Show file size and first few lines
                file_size = md_file.stat().st_size
                print(f"   File size: {file_size} bytes")
                
                with open(md_file, 'r', encoding='utf-8') as f:
                    first_lines = [f.readline().strip() for _ in range(3)]
                print(f"   First lines: {first_lines}")
            else:
                print("❌ Failed to create markdown file")
                return False
        
        # Test 5: Test processing specific folder
        print("\nTest 5: Testing specific folder processing...")
        result_path = converter.process_specific_folder(0)
        if result_path and result_path.exists():
            print(f"✅ Successfully processed folder 0: {result_path.name}")
        else:
            print("❌ Failed to process specific folder")
            return False
        
        # Test 6: Test processing all folders
        print("\nTest 6: Testing batch processing...")
        result = converter.process_all_folders()
        if result["status"] == "success":
            print(f"✅ Batch processing successful:")
            print(f"   Processed: {result['processed_folders']}/{result['total_folders']} folders")
            print(f"   Created files: {len(result['created_files'])}")
        else:
            print(f"❌ Batch processing failed: {result}")
            return False
        
        print("\n" + "=" * 40)
        print("🎉 All tests passed! Enhanced workflow is working correctly.")
        
        # Show final results
        raw_md_folder = Path("raw_md")
        if raw_md_folder.exists():
            md_files = list(raw_md_folder.glob("*.md"))
            print(f"\nGenerated markdown files ({len(md_files)}):")
            for md_file in md_files:
                print(f"  - {md_file.name}")
        
        return True
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False


def cleanup_test_files():
    """Clean up test files (optional)."""
    print("\nCleaning up test files...")
    
    # Remove sample staging folders
    staging_folder = Path("staging")
    if staging_folder.exists():
        for folder in staging_folder.iterdir():
            if folder.is_dir() and folder.name.startswith("chunks_"):
                print(f"Removing: {folder}")
                import shutil
                shutil.rmtree(folder)
    
    # Remove generated markdown files
    raw_md_folder = Path("raw_md")
    if raw_md_folder.exists():
        for md_file in raw_md_folder.glob("*.md"):
            print(f"Removing: {md_file}")
            md_file.unlink()
    
    print("✅ Cleanup complete")


if __name__ == "__main__":
    print("Enhanced Workflow Test Suite")
    print("=" * 50)
    
    # Run tests
    success = test_enhanced_workflow()
    
    if success:
        print("\n✅ Enhanced workflow is ready to use!")
        print("\nNext steps:")
        print("1. Run 'python enhanced_workflow.py --list-folders' to see available folders")
        print("2. Run 'python enhanced_workflow.py' to process all folders")
        print("3. Check the 'raw_md' folder for generated markdown files")
    else:
        print("\n❌ Tests failed. Please check the error messages above.")
        sys.exit(1)
    
    # Ask if user wants to clean up
    if len(sys.argv) > 1 and sys.argv[1] == "--cleanup":
        cleanup_test_files()
